import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/features/auth/lib/authOptions';
import { prisma } from '@/shared/lib/prisma';
import { uploadFile } from '@/shared/lib/s3logoStorage';
import { PublicImageType, Prisma } from '@prisma/client';
import sharp from 'sharp';

// GET images by type
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const typeParam = searchParams.get('type');
    
    if (!typeParam || !Object.values(PublicImageType).includes(typeParam as PublicImageType)) {
      return NextResponse.json({ error: 'Invalid or missing image type' }, { status: 400 });
    }

    const imageType = typeParam as PublicImageType;

    const images = await prisma.publicImage.findMany({
      where: { type: imageType },
      orderBy: { createdAt: 'desc' },
    });

    // Transform to include URLs using the same pattern as existing endpoints
    const bucket = process.env.NEXT_PUBLIC_HETZNER_STORAGE_BUCKET_ICONS!;
    const endpoint = process.env.NEXT_PUBLIC_HETZNER_STORAGE_ENDPOINT!;
    const host = endpoint.replace(/^https?:\/\//, '');

    const imagesWithUrls = images.map((image) => ({
      ...image,
      url: `https://${bucket}.${host}/${image.objectKey}`,
    }));

    // Return format consistent with existing endpoints
    if (imageType === PublicImageType.BLOG_IMAGE) {
      return NextResponse.json({
        data: imagesWithUrls,
        message: 'Images retrieved successfully',
      });
    } else {
      return NextResponse.json(imagesWithUrls);
    }
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST upload a new image
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session || session.user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const publicName = formData.get('publicName') as string;
    const imageType = formData.get('type') as PublicImageType;
    const resizeParam = formData.get('resize') as string;

    if (!file || !name || !publicName || !imageType) {
      return NextResponse.json(
        { error: 'Missing required fields: file, name, publicName, type' },
        { status: 400 }
      );
    }

    if (!Object.values(PublicImageType).includes(imageType)) {
      return NextResponse.json(
        { error: 'Invalid image type' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'File must be an image' },
        { status: 400 }
      );
    }

    // Sanitize public name
    const sanitizedPublicName = publicName
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^a-zA-Z0-9-_.]/g, '')
      .toLowerCase();

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    let buf = Buffer.from(arrayBuffer);

    // Get image metadata and optionally resize
    let width: number | undefined;
    let height: number | undefined;

    try {
      const sharpImage = sharp(buf);
      const metadata = await sharpImage.metadata();
      width = metadata.width;
      height = metadata.height;

      // Handle resize if requested
      if (resizeParam) {
        const resize = JSON.parse(resizeParam);
        if (resize.width && resize.height) {
          buf = await sharpImage
            .resize(resize.width, resize.height, {
              fit: 'contain',
              background: { r: 0, g: 0, b: 0, alpha: 0 },
            })
            .toBuffer();
          width = resize.width;
          height = resize.height;
        }
      }
    } catch (error) {
      console.error('Error processing image:', error);
      return NextResponse.json(
        { error: 'Error processing image' },
        { status: 400 }
      );
    }

    // Determine S3 key prefix based on image type
    let prefix: string;
    switch (imageType) {
      case PublicImageType.EMAIL_ICON:
        prefix = 'email';
        break;
      case PublicImageType.BLOG_IMAGE:
        prefix = 'blog';
        break;
      default:
        prefix = 'images';
        break;
    }

    // Create S3 key
    const key = `${prefix}/${sanitizedPublicName}`;

    // For EMAIL_ICON, preserve original MIME type; for others, convert to JPEG
    let finalMimeType = file.type;
    if (imageType !== PublicImageType.EMAIL_ICON) {
      buf = await sharp(buf).jpeg({ quality: 85 }).toBuffer();
      finalMimeType = 'image/jpeg';
    }

    // Upload to S3
    const bucket = process.env.NEXT_PUBLIC_HETZNER_STORAGE_BUCKET_ICONS!;
    await uploadFile(buf, key, finalMimeType, bucket);

    try {
      // Create database record
      const record = await prisma.publicImage.create({
        data: {
          name,
          objectKey: key,
          mimeType: finalMimeType,
          fileSize: buf.byteLength,
          width: width || null,
          height: height || null,
          type: imageType,
        },
      });

      // Return record with URL using the same pattern as existing endpoints
      const endpoint = process.env.NEXT_PUBLIC_HETZNER_STORAGE_ENDPOINT!;
      const host = endpoint.replace(/^https?:\/\//, '');

      const recordWithUrl = {
        ...record,
        url: `https://${bucket}.${host}/${key}`,
      };

      return NextResponse.json(recordWithUrl, { status: 201 });
    } catch (dbError) {
      console.error('Database error:', dbError);
      
      // Handle unique constraint violations
      if (dbError instanceof Prisma.PrismaClientKnownRequestError && dbError.code === 'P2002') {
        if (imageType === PublicImageType.EMAIL_ICON) {
          return NextResponse.json(
            { error: 'Ein Icon mit diesem Namen oder öffentlichem Namen existiert bereits' },
            { status: 409 }
          );
        } else {
          return NextResponse.json(
            { error: 'Ein Bild mit diesem Namen oder öffentlichem Namen existiert bereits' },
            { status: 409 }
          );
        }
      }

      return NextResponse.json(
        { error: 'Fehler beim Erstellen des Datenbank-Eintrags' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
