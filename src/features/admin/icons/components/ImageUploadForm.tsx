import React, { useState, ChangeEvent, useEffect, DragEvent } from 'react';
import {
  ArrowUpTrayIcon,
  XMarkIcon,
  PhotoIcon,
  ArrowPathIcon,
  SunIcon,
  MoonIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';
import { PublicImageType } from '@prisma/client';
import { useImages } from '../hooks/useImages';
import { showErrorDialog } from '@/shared/utils/dialogs';
import { ImageUploadOptions } from '../types/imageTypes';

interface ImageUploadFormProps {
  imageType: PublicImageType;
  onUpload: (options: ImageUploadOptions) => Promise<boolean>;
}

export default function ImageUploadForm({ imageType, onUpload }: ImageUploadFormProps) {
  const { images } = useImages(imageType);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [name, setName] = useState('');
  const [publicName, setPublicName] = useState('');
  const [nameError, setNameError] = useState<string | null>(null);
  const [publicNameError, setPublicNameError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [previewSize, setPreviewSize] = useState(100);
  const [imageNaturalSize, setImageNaturalSize] = useState({ width: 0, height: 0 });
  const [enableResize, setEnableResize] = useState(false);
  const [fileExtension, setFileExtension] = useState('');
  const [isDragging, setIsDragging] = useState(false);

  // Validation functions
  const validateName = (value: string) => {
    if (!value.trim()) {
      setNameError('Name ist erforderlich');
      return false;
    }
    if (value.length < 2) {
      setNameError('Name muss mindestens 2 Zeichen lang sein');
      return false;
    }
    setNameError(null);
    return true;
  };

  const validatePublicName = (value: string) => {
    if (!value.trim()) {
      setPublicNameError('Öffentlicher Name ist erforderlich');
      return false;
    }
    if (value.length < 2) {
      setPublicNameError('Öffentlicher Name muss mindestens 2 Zeichen lang sein');
      return false;
    }
    
    // Check for duplicates
    const finalPublicName = value + fileExtension;
    const isDuplicate = images.some(image => 
      image.objectKey.endsWith(finalPublicName) && image.objectKey !== finalPublicName
    );
    
    if (isDuplicate) {
      setPublicNameError('Ein Bild mit diesem öffentlichen Namen existiert bereits');
      return false;
    }
    
    setPublicNameError(null);
    return true;
  };

  // Handle file selection
  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    
    // Extract filename without extension for both fields
    const baseName = file.name.replace(/\.[^/.]+$/, '');
    setName(baseName);
    setPublicName(baseName);
    
    // Store file extension
    const extension = file.name.substring(file.name.lastIndexOf('.'));
    setFileExtension(extension);
    
    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    
    // Get image dimensions
    if (file.type.startsWith('image/')) {
      const img = new window.Image();
      img.onload = () => {
        setImageNaturalSize({ width: img.width, height: img.height });
      };
      img.src = url;
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragEnter = (e: DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Calculate display dimensions for resize
  const getDisplayDimensions = () => {
    if (!imageNaturalSize.width || !imageNaturalSize.height) {
      return { width: previewSize, height: previewSize };
    }
    
    const aspectRatio = imageNaturalSize.width / imageNaturalSize.height;
    if (aspectRatio > 1) {
      return { width: previewSize, height: Math.round(previewSize / aspectRatio) };
    } else {
      return { width: Math.round(previewSize * aspectRatio), height: previewSize };
    }
  };

  const displayDimensions = getDisplayDimensions();

  async function handleUpload() {
    if (!selectedFile) return;
    if (nameError || publicNameError) return;

    setUploading(true);

    // Add extension back to publicName
    const finalPublicName = publicName + fileExtension;

    // Pass resize dimensions if enabled
    const resizeOptions = enableResize
      ? {
          width: displayDimensions.width,
          height: displayDimensions.height,
        }
      : undefined;

    const success = await onUpload({
      file: selectedFile,
      name,
      publicName: finalPublicName,
      resize: resizeOptions,
    });

    if (success) {
      // Reset form
      setSelectedFile(null);
      setName('');
      setPublicName('');
      setPreviewUrl(null);
      setFileExtension('');
      setEnableResize(false);
      setImageNaturalSize({ width: 0, height: 0 });
    }

    setUploading(false);
  }

  // Cleanup preview URL on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const getImageTypeLabel = () => {
    switch (imageType) {
      case PublicImageType.EMAIL_ICON:
        return 'Icon';
      case PublicImageType.BLOG_IMAGE:
        return 'Blog-Bild';
      default:
        return 'Bild';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {getImageTypeLabel()} hochladen
      </h3>

      {!selectedFile ? (
        <div
          className={`border-2 border-dashed ${
            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
          } p-8 rounded-lg text-center cursor-pointer transition-colors mb-6 group`}
          onClick={() => document.getElementById('file-input')?.click()}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <ArrowUpTrayIcon
            className={`h-10 w-10 mx-auto mb-3 ${
              isDragging ? 'text-blue-500' : 'text-gray-400 group-hover:text-blue-500'
            } transition-colors`}
          />
          <div
            className={`${isDragging ? 'text-gray-700' : 'text-gray-500 group-hover:text-gray-700'} transition-colors`}
          >
            Klicken oder Datei hier ablegen
          </div>
          <input
            id="file-input"
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      ) : (
        <div className="space-y-4">
          {/* File preview and info */}
          <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex-shrink-0">
              {previewUrl ? (
                <div className="w-16 h-16 relative">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-full object-contain rounded"
                  />
                </div>
              ) : (
                <PhotoIcon className="h-16 w-16 text-gray-400" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {selectedFile.name}
              </p>
              <p className="text-sm text-gray-500">
                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </p>
              {imageNaturalSize.width > 0 && (
                <p className="text-sm text-gray-500">
                  {imageNaturalSize.width} × {imageNaturalSize.height} px
                </p>
              )}
            </div>
            <button
              onClick={() => {
                setSelectedFile(null);
                setPreviewUrl(null);
                setName('');
                setPublicName('');
                setFileExtension('');
                setEnableResize(false);
              }}
              className="flex-shrink-0 text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Form fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                  validateName(e.target.value);
                }}
                className={`w-full p-2 border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
                  nameError ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Interner Name"
              />
              {nameError && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                  {nameError}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="publicName" className="block text-sm font-medium text-gray-700 mb-1">
                Öffentlicher Name
              </label>
              <div className="flex">
                <input
                  type="text"
                  id="publicName"
                  value={publicName}
                  onChange={(e) => {
                    setPublicName(e.target.value);
                    validatePublicName(e.target.value);
                  }}
                  className={`flex-1 p-2 border rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${
                    publicNameError ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Dateiname"
                />
                <span className="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  {fileExtension}
                </span>
              </div>
              {publicNameError && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                  {publicNameError}
                </p>
              )}
            </div>
          </div>

          {/* Resize options */}
          {imageNaturalSize.width > 0 && (
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableResize"
                  checked={enableResize}
                  onChange={(e) => setEnableResize(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enableResize" className="ml-2 text-sm text-gray-700">
                  Größe anpassen
                </label>
              </div>

              {enableResize && (
                <div className="pl-6 space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Größte Seite: {previewSize}px
                    </label>
                    <input
                      type="range"
                      min="50"
                      max="2000"
                      value={previewSize}
                      onChange={(e) => setPreviewSize(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>50px</span>
                      <span>2000px</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600">
                    Resultierende Größe: {displayDimensions.width} × {displayDimensions.height} px
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Upload button */}
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              disabled={uploading || !!nameError || !!publicNameError || !name || !publicName}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploading ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                  Wird hochgeladen...
                </>
              ) : (
                <>
                  <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
                  {getImageTypeLabel()} hochladen
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
