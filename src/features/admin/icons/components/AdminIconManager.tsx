'use client';

import React, { useState } from 'react';
import { useImages } from '../hooks/useImages';
import ImageList from './ImageList';
import ImageUploadForm from './ImageUploadForm';
import { IMAGE_CATEGORIES, ImageCategory } from '../types/imageTypes';
import IconUploadForm from '@/features/admin/icons/components/IconUploadForm';

export default function AdminIconManager() {
  const [activeCategory, setActiveCategory] = useState<ImageCategory>(IMAGE_CATEGORIES[0]);

  const { images, error, loading, uploadImage, deleteImage } = useImages(activeCategory.type);

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {IMAGE_CATEGORIES.map((category) => (
              <button
                key={category.key}
                onClick={() => setActiveCategory(category)}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeCategory.key === category.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                aria-current={activeCategory.key === category.key ? 'page' : undefined}
              >
                {category.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Upload Form */}
      <ImageUploadForm imageType={activeCategory.type} onUpload={uploadImage} />

      {/* Error Display */}
      {error && <div className="text-red-600 mb-4 p-3 bg-red-50 rounded-md">{error}</div>}

      {/* Loading State */}
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : (
        <ImageList
          images={images}
          onDelete={deleteImage}
          layout="grid-4"
          showDeleteButton={true}
        />
      )}
    </div>
  );
}
