import { useCallback, useEffect, useState } from 'react';
import { PublicImageType } from '@prisma/client';
import { ImageWithUrl, ImageUploadOptions } from '../types/imageTypes';

export function useImages(imageType: PublicImageType) {
  const [images, setImages] = useState<ImageWithUrl[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Determine API endpoint based on image type
  const getApiEndpoint = useCallback(() => {
    switch (imageType) {
      case PublicImageType.EMAIL_ICON:
        return '/api/admin/icons';
      case PublicImageType.BLOG_IMAGE:
        return '/api/admin/blog-images';
      default:
        return `/api/admin/images?type=${imageType}`;
    }
  }, [imageType]);

  // Load existing images
  const fetchImages = useCallback(async () => {
    setLoading(true);
    try {
      const res = await fetch(getApiEndpoint());
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      
      const data = await res.json();
      // Handle different response formats
      const imageData = Array.isArray(data) ? data : (data.data || []);
      setImages(imageData);
      setError(null);
    } catch (err) {
      setError('Bilder konnten nicht geladen werden');
      console.error('Error fetching images:', err);
    } finally {
      setLoading(false);
    }
  }, [getApiEndpoint]);

  useEffect(() => {
    fetchImages();
  }, [fetchImages]);

  // Upload image
  const uploadImage = async (options: ImageUploadOptions): Promise<boolean> => {
    const { file, name, publicName, resize } = options;
    setError(null);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', name);
    formData.append('publicName', publicName);

    // Add resize parameters if provided
    if (resize) {
      formData.append('resize', JSON.stringify(resize));
    }

    try {
      const res = await fetch(getApiEndpoint(), {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const txt = await res.text();
        throw new Error(txt);
      }

      const newImage: ImageWithUrl = await res.json();
      setImages((prev) => [newImage, ...prev]);
      return true;
    } catch (err) {
      setError(`Upload failed: ${err instanceof Error ? err.message : String(err)}`);
      return false;
    }
  };

  // Delete image
  const deleteImage = async (id: string): Promise<void> => {
    setError(null);

    try {
      const endpoint = imageType === PublicImageType.EMAIL_ICON 
        ? `/api/admin/icons/${id}`
        : imageType === PublicImageType.BLOG_IMAGE
        ? `/api/admin/blog-images/${id}`
        : `/api/admin/images/${id}`;

      const res = await fetch(endpoint, {
        method: 'DELETE',
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
      }

      // Remove the deleted image from the state
      setImages((prev) => prev.filter((image) => image.id !== id));
    } catch (err) {
      setError(`Delete failed: ${err instanceof Error ? err.message : String(err)}`);
      throw err;
    }
  };

  return {
    images,
    error,
    loading,
    uploadImage,
    deleteImage,
    refetch: fetchImages,
  };
}
